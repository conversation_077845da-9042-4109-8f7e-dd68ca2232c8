/************************************************************************/
/*                                                                      */
/*                      INCLUDE FILE OVP_BUFF                           */
/*                                                                      */
/*  Author : <PERSON>                                               */
/*  Date :   Oct. 1990                                                  */
/*                                                                      */
/************************************************************************/

#ifndef IF_DIR
#define  IF_DIR           "/cae/if/ifutil/"
#define  SGIOVP_DIR       "IFUTIL_DIR"
#endif

#define  FILE_ERROR       2  /*** eas error code for fail open ***/

#define  FILE_NOT_FOUND  -1  /*** all ovp code ***/
#define  IN_PENDING       0
#define  COMPLETED        1
#define  CONTINUE         2

#define  INIT_STATE       1
#define  OPEN_STATE       2
#define  READ_STATE       3
#define  CLOSE_STATE      4

#define  FOREGRD_STATE    5
#define  PENDING_STATE    6

  /*** aloocation space and record len for buffio ***/

#define BUFF_SPACE    10000
#define SLICE_LEN      5000

#define FILE_BUFF_FULL "=> OVP_BUFF: Loading buffer full\n"
struct file_struct
  {
     int     nb_alloc;
     int     pos_read;
     int     pos_eof;
	   int     pos_file;
     char   *data;
  };

typedef struct file_struct File_data;

