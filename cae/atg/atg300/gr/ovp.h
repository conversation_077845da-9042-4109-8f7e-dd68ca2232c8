#include <stdio.h>

extern FILE *visaout;   /* Output file descriptor for all screen output */

/***********************************************************************/
/*    The IPT structure defines the coordinate data structure for      */
/*    integer                                                          */
/***********************************************************************/

typedef struct integer_coordinate {
   int x;
   int y;
} Ipt;

/***********************************************************************/
/*    The HLS_color structure defines the three values required when   */
/*    setting Hu<PERSON>, Lightness, and Shade for HLS coloring.              */
/***********************************************************************/

typedef struct HLS_color_codes {
   int hue;
   int light;
   int shade;
} HLS_color;

/***********************************************************************/
/*    The PT structure defines the coordinate data structure for       */
/*    float                                                            */
/***********************************************************************/

typedef struct point_struc {
   double x;
   double y;
} Pt;

typedef struct win_def {
   Pt min;
   Pt max;
} Window;


typedef struct float_coordinate_system {
   Pt xy1;
   Pt xy2;
}   Pts;

typedef struct int_coordinate_system {
   Ipt xy1;
   Ipt xy2;
}   Ipts;

#define round(i)    ((int)((i>0) ? (i+0.5) : (i-0.5)))

/*  Input-output file description */

#define  OUTPUT   "VISAOUT"  
#define  INPUT    "VISAIN"
#define  MSG_OUTPUT "MSGOUT"

/*  Enumerated type used for the tolerance_code */

enum tolerance_code_value
   { in, maybe, out};
