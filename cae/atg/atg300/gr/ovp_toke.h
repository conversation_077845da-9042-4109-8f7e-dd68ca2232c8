/************************************************************************/
/*									*/
/*			INCLUDE FILE TOKEN_CD				*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/************************************************************************/

#define PROJECT                  0
#define AIRCRAFT                 1
#define TITLE                    2
#define TYPE                     3
#define ORIGIN                   4
#define REFNO                    5
#define SMPLRT                   6
#define DOCUMENT                 7
#define NUMPTS                   8
#define FIGURE                   9
#define TOLPERC                 10
#define ORIENTATION             11
#define PAGEHEIGHT              12
#define TOLABS                  13
#define PAGE                    14
#define PAGEWIDTH               15
#define XNAME                   16
#define XLABEL                  17
#define YNAME                   18
#define YLA<PERSON><PERSON>                  19
#define XPAGE                   20
#define YPAGE                   21
#define <PERSON>LE<PERSON>                    22
#define Y<PERSON><PERSON>                    23
#define XUNITS                  24
#define YUNITS                  25
#define XMIN                    26
#define YMIN                    27
#define XMAX                    28
#define YMAX                    29
#define XGRIDC                  30
#define YGRIDC                  31
#define XGRIDF                  32
#define YGRIDF                  33
#define SUBTITLE                34
#define NB_PARAMETERS_PAGE	35
#define X_VALUE                 36
#define Y_VALUE                 37
#define NEW_PARAM               38
#define NEW_PAGE		39
#define END_OF_GENERAL		40
#define NB_POINTS		41
#define NB_PARAMETERS 	        42
#define INITIAL_CONDITIONS      43
