/************************************************************************/
/*									*/
/*                     INCLUDE FILE OVP_MESSAGE		 		*/
/*									*/
/* Author: <PERSON>     		     	  	 		*/
/* Date :  June 1990							*/
/*	      	 							*/
/*  Contains the messages used by the OVP system.			*/
/************************************************************************/

#define CHECK_MSG  		"VSA Binary File"
#define NO_PREV_PAGE		"NO PREVIOUS PAGE"
#define NO_NEXT_PAGE		"NO FOLLOWING PAGE"
#define NOT_READY		"PAGE NOT READY"
#define NON_EXISTENT 		"NON-EXISTENT PAGE"
#define CTS_OPEN_ERROR		"UNAVAILABLE CTS FILE"
#define MST_OPEN_ERROR		"UNAVAILABLE MST FILE"
#define CTS_READ_ERROR		"CTS READ ERROR"
#define RAMP_READ_ERROR		"RAMP READ ERROR"
#define MST_READ_ERROR		"MASTER READ ERROR"
#define CTS_CHECK_MSG_ERROR	"WRONG CHECK IN CTS"
#define RAMP_CHECK_MSG_ERROR	"WRONG CHECK IN RAMP"
#define MST_CHECK_MSG_ERROR	"WRONG CHECK IN MST"
#define INSUFFICIENT_SPACE	"INSUFFICIENT SPACE"
#define NO_TESTID_ERROR		"INVALID TEST ID."
#define M_LOAD_ERROR		"UNABLE TO LOAD"
#define LOADING                 "LOADING "
#define END_LOADING             "FILES ARE LOADED"
#define PRINT_PRINTER           "PRINTER: PRINTRONIX"
#define CALC_PRINTER            "PRINTER: COLORMASTER"
#define HP_PRINTER              "PRINTER: HP LASERJET"
#define HCPY_STARTED            "HARDCOPY STARTED"
#define NOMORE_DISP             "NO GRAPHIC CHANNEL"
#define QUEUE_FULLERR           "QUEUE FULL"
#define NOTC_FILE               "NO TEST CASES FILE"
#define LOGNAME_ERR             "HOST LOGNAME ERROR"
#define PLOT_BUFF_FULL          "PLOTING BUFFER FULL"
#define KILL_TEST               "KILL TEST CASE"
#define END_SESSION             "END SESSION"
#define RUNNING_RT              "RUNNING REAL TIME"
#define ERROR_RT                "REAL TIME ERROR"
#define CTS_CRASHED             "CTS ABORTED"

#define CHOOSE_A_CHAP           "CHOOSE A CHAPTER"
#define CHOOSE_CHAP             "CHOOSE CHAPTERS"
#define CHOOSE_A_SECT           "CHOOSE A SECTION"
#define CHOOSE_SECT             "CHOOSE SECTIONS"
#define CHOOSE_TESTCASE         "CHOOSE TEST CASES"
#define CHOOSE_SET              "CHOOSE DATA SET"
#define DONE_RETURN             "<DONE> TO RETURN"
#define DONE_NEXT               "<DONE> FOR NEXT TEST"
#define DONE_SLCT               "<DONE> TO MAKE SLCT"
#define DONE_END                "<DONE> TO END SLCT"
#define START_BEGIN             "<START> FIRST TEST"
#define SLCT_OPT                "SELECT OPTIONS"
#define LAST_TEST               "LAST TEST RUNNING"
#define MST_LOAD                "LOADING MASTER FILE"
#define CTS_LOAD                "LOADING CTS FILE"
#define NO_CTS_FILE             "NO CTS FILE"
#define EMPTY_SET               "DATA SET EMPTY!"
#define WORK_MSG                "WORKING"

