/************************************************************************/
/*									*/
/*                         INCLUDE FILE HCPY_MESSAGE	 		*/
/* Author: <PERSON><PERSON><PERSON>lette     		      	  	 		*/
/* Date  : 01.87							*/
/*	      	 							*/
/*  Contains the messages used by the Hardcopy program.			*/
/************************************************************************/

#define CHECK_MSG  		"VSA Binary File"
#define CTS_OPEN_ERROR		"Unable to open the CTS file"
#define MST_OPEN_ERROR		"Unable to open the Master file"
#define CTS_READ_ERROR		"Unable to read the CTS file"
#define RAMP_READ_ERROR		"Unable to read the Ramp file"
#define MST_READ_ERROR		"Unable to read the Master file"
#define CTS_CHECK_MSG_ERROR	"Wrong check message in CTS file"
#define RAMP_CHECK_MSG_ERROR	"Wrong check message in Ramp file"
#define MST_CHECK_MSG_ERROR	"Wrong check message in Master file"
#define INSUFFICIENT_SPACE	"Insufficient space for your request"
#define NO_TESTID_ERROR		"Invalid test id. parameter"
#define M_LOAD_ERROR		"Unable to load the files"
#define HARDCOPY                "Preparing hardcopy"
#define PRINT_PRINTER           "Printer used is printronix"
#define CALC_PRINTER            "Printer used is ColorMaster printer"
