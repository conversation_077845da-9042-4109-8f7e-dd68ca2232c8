/************************************************************************/
/*									*/
/*			INCLUDE FILE PRECOMP				*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/* define system used							*/
/************************************************************************/

/***-----------------------------***/
/*** OVP output screen selection ***/
/***-----------------------------***/
/*#define VISTA "VISTA" */
/*#define SGI     "SGI" */

/***-----------------------------***/
/***       host selection        ***/
/***-----------------------------***/
/*#define PCHOST "PCHOST"*/
/*#define VAXHOST "VAXHOST"*/
#define UNIXHOST "UNIXHOST"

/***-----------------------------***/
/***    UNIX system selection    ***/
/***-----------------------------***/
/* #define IBM_RT "IBM_RT" */

/***-----------------------------***/
/*** REAL TIME option available  ***/
/***       on this system        ***/
/***-----------------------------***/
/*#define REAL_TIME_OPTION 1*/

/***-----------------------------***/
/***  ColorMaster available on   ***/ 
/***         this system         ***/
/***-----------------------------***/
/*#define COLOR_OPTION 1*/

/***-----------------------------***/
/***       Special DEFINE        ***/
/***   For system using :        ***/
/***     - ReceiveFromHost       ***/
/***     - ReceiveArrayFromHost  ***/
/***     - TransmitToHost        ***/
/***     - TransmitArrayToHost   ***/
/***-----------------------------***/
/*#define QOVP "QOVP"*/

/***-----------------------------***/
/***   redefinition of caelib    ***/
/***    functions for IBM_RT     ***/
/***-----------------------------***/
/*#ifdef IBM_RT                     */
/*#   define cae_trnl_ cae_trnl     */
/*#   define rev_curr_c_ rev_curr_c */
/*#   define rev_next_c_ rev_next_c */
/*#endif                            */

/***-----------------------------***/
/***  communication simulation   ***/
/***            mode             ***/
/***-----------------------------***/
/* #define SIMCOMM 1 */

/***-----------------------------***/
/***   debug mode for ovp_plot   ***/
/***-----------------------------***/
/* #define DEBUG_OVP_PLOT 1 */
